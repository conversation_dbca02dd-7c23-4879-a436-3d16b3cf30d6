# Firespoon API Local Development Environment Configuration
# Copy this file to .env.local and fill in your values

# Node environment
NODE_ENV=local

# Server Configuration
SERVER_URL=http://localhost:8000
PORT=8000

# JWT Secret for authentication (REQUIRED)
# Generate a strong secret key for JWT token signing
# Example: openssl rand -base64 32
JWT_SECRET=your-local-jwt-secret-here

# Database Configuration
CONNECTION_STRING=mongodb://localhost:27017/firespoon_local
REDIS_URL=redis://localhost:6379
REDIS_ENABLE_TLS=false

# Firebase Configuration (REQUIRED)
# Option 1: Use environment variable (recommended)
# FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account","project_id":"your-project",...}'
# Option 2: Place serviceAccountKey.json in project root (development only)

# WhatsApp API Configuration
WS_API_URL=https://your-whatsapp-api.com
WS_AUTH_URL=https://your-auth-endpoint.com
WS_BOUNDARY=your-boundary-string

# Payemoji credentials
PAYEMOJI_CLIENT_ID=your-client-id
PAYEMOJI_CLIENT_SECRET=your-client-secret
PAYEMOJI_WEBHOOK_SECRET=your-webhook-secret
PAYEMOJI_AGENT_ID=your-agent-id

# Stripe API keys
STRIPE_SECRET_KEY=sk_test_your_test_key
STRIPE_WEBHOOK_SECRET=whsec_your_test_webhook_secret
STRIPE_CHECKOUT_BASE_URL=http://localhost:3000

# WhatsApp Service Configuration
WS_GRANT_TYPE=client_credentials
WS_SCOPE=your-scope
WS_B2C_POLICY=your-policy

# Service URLs
ORDER_MANAGEMENT_URL=https://your-order-management.com
ADDRESS_MANAGEMENT_URL=https://your-address-management.com

# Template IDs
WS_TEMPLATE_BASIC_TEXT=your-template-id

# Dialog Configuration
DIALOG_SESSION_TOKEN_BYTES=32
DIALOG_SESSION_TTL_SECONDS=3600

# Logging
LOG_LEVEL=debug
CONSOLE_LOG_LEVEL=debug
FILE_LOG_LEVEL=info

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# Currency Configuration
DEFAULT_CURRENCY=USD
DEFAULT_CURRENCY_SYMBOL=$
